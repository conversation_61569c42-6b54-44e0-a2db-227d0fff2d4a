#include "ext_process_midi.h"
#include "alsa_midi.h" // For MIDIMessage definition
#include <iostream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <stdexcept>

#ifdef _WIN32
#include <windows.h>
#include <io.h> // For _open_osfhandle
#include <fcntl.h> // For _O_TEXT
#else
#include <cstdio> // For popen, pclose
#include <cstring> // For strerror
#include <cerrno> // For errno
#endif

ExtProcessMIDIInput::ExtProcessMIDIInput()
    : initialized_(false)
    , input_active_(false)
    , should_stop_(false)
#ifdef _WIN32
    , child_stdout_read_(NULL)
    , child_stderr_read_(NULL)
#else
    , child_pid_(-1)
#endif
{
#ifdef _WIN32
    ZeroMemory(&pi_proc_info_, sizeof(PROCESS_INFORMATION));
    ZeroMemory(&sa_pipe_attr_, sizeof(SECURITY_ATTRIBUTES));
#else
    stdout_pipe_[0] = -1;
    stdout_pipe_[1] = -1;
    stderr_pipe_[0] = -1;
    stderr_pipe_[1] = -1;
#endif
}

ExtProcessMIDIInput::~ExtProcessMIDIInput() {
    Cleanup();
}

bool ExtProcessMIDIInput::Initialize() {
    if (initialized_) {
        return true;
    }

#ifdef _WIN32
    // Set the bInheritHandle flag so pipe handles are inherited.
    sa_pipe_attr_.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa_pipe_attr_.bInheritHandle = TRUE;
    sa_pipe_attr_.lpSecurityDescriptor = NULL;
#endif

    initialized_ = true;
    std::cout << "External Process MIDI input system initialized." << std::endl;
    return true;
}

void ExtProcessMIDIInput::Cleanup() {
    if (!initialized_) {
        return;
    }

    StopInput();

#ifdef _WIN32
    if (child_stdout_read_ != NULL) {
        CloseHandle(child_stdout_read_);
        child_stdout_read_ = NULL;
    }
    if (child_stderr_read_ != NULL) {
        CloseHandle(child_stderr_read_);
        child_stderr_read_ = NULL;
    }
    if (pi_proc_info_.hProcess != NULL) {
        CloseHandle(pi_proc_info_.hProcess);
        ZeroMemory(&pi_proc_info_, sizeof(PROCESS_INFORMATION));
    }
    if (pi_proc_info_.hThread != NULL) {
        CloseHandle(pi_proc_info_.hThread);
    }
#else
    if (stdout_pipe_[0] != -1) {
        close(stdout_pipe_[0]);
        stdout_pipe_[0] = -1;
    }
    if (stdout_pipe_[1] != -1) {
        close(stdout_pipe_[1]);
        stdout_pipe_[1] = -1;
    }
    if (stderr_pipe_[0] != -1) {
        close(stderr_pipe_[0]);
        stderr_pipe_[0] = -1;
    }
    if (stderr_pipe_[1] != -1) {
        close(stderr_pipe_[1]);
        stderr_pipe_[1] = -1;
    }
    if (child_pid_ != -1) {
        // Ensure child process is reaped if it's still running
        int status;
        waitpid(child_pid_, &status, WNOHANG); // Non-blocking wait
        child_pid_ = -1;
    }
#endif

    initialized_ = false;
    std::cout << "External Process MIDI input system cleaned up." << std::endl;
}

bool ExtProcessMIDIInput::StartInput(const std::string& executable_path, const std::vector<std::string>& args) {
    if (!initialized_) {
        std::cerr << "External Process MIDI input system not initialized." << std::endl;
        return false;
    }
    if (input_active_) {
        std::cout << "External Process MIDI input already active." << std::endl;
        return true;
    }

    // Clear previous log
    ClearStderrLog();

#ifdef _WIN32
    HANDLE stdout_write_pipe, stderr_write_pipe;

    // Create pipes for stdout and stderr
    if (!CreatePipe(&child_stdout_read_, &stdout_write_pipe, &sa_pipe_attr_, 0)) {
        std::cerr << "Failed to create stdout pipe. Error: " << GetLastError() << std::endl;
        return false;
    }
    if (!SetHandleInformation(child_stdout_read_, HANDLE_FLAG_INHERIT, 0)) {
        std::cerr << "Failed to set stdout pipe handle info. Error: " << GetLastError() << std::endl;
        CloseHandle(child_stdout_read_);
        CloseHandle(stdout_write_pipe);
        return false;
    }

    if (!CreatePipe(&child_stderr_read_, &stderr_write_pipe, &sa_pipe_attr_, 0)) {
        std::cerr << "Failed to create stderr pipe. Error: " << GetLastError() << std::endl;
        CloseHandle(child_stdout_read_);
        CloseHandle(stdout_write_pipe);
        return false;
    }
    if (!SetHandleInformation(child_stderr_read_, HANDLE_FLAG_INHERIT, 0)) {
        std::cerr << "Failed to set stderr pipe handle info. Error: " << GetLastError() << std::endl;
        CloseHandle(child_stdout_read_);
        CloseHandle(stdout_write_pipe);
        CloseHandle(child_stderr_read_);
        CloseHandle(stderr_write_pipe);
        return false;
    }

    // Set up STARTUPINFO structure
    STARTUPINFO si;
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);
    si.hStdError = stderr_write_pipe;
    si.hStdOutput = stdout_write_pipe;
    si.dwFlags |= STARTF_USESTDHANDLES;

    // Build command line string
    std::string command_line = "\"" + executable_path + "\"";
    if (!args.empty()) {
        command_line += " " + args[0]; // args[0] now contains the full argument string
    }

    std::cout << "Debug: executable_path = " << executable_path << std::endl;
    std::cout << "Debug: args count = " << args.size() << std::endl;
    for (size_t i = 0; i < args.size(); ++i) {
        std::cout << "Debug: arg[" << i << "] = " << args[i] << std::endl;
    }
    std::cout << "Debug: Constructed command_line = " << command_line << std::endl;

    // Create the child process.
    if (!CreateProcess(NULL,   // No module name (use command line)
                       const_cast<char*>(command_line.c_str()), // Command line
                       NULL,           // Process handle not inheritable
                       NULL,           // Thread handle not inheritable
                       TRUE,           // Set handles to be inherited.
                       CREATE_NO_WINDOW, // Do not create a console window
                       NULL,           // Use parent's environment block
                       NULL,           // Use parent's starting directory
                       &si,            // Pointer to STARTUPINFO structure
                       &pi_proc_info_)) // Pointer to PROCESS_INFORMATION structure
    {
        std::cerr << "Failed to create process. Error: " << GetLastError() << ". Command: " << command_line << std::endl;
        CloseHandle(child_stdout_read_);
        CloseHandle(stdout_write_pipe);
        CloseHandle(child_stderr_read_);
        CloseHandle(stderr_write_pipe);
        return false;
    }
    std::cout << "Successfully created process with command: " << command_line << std::endl;

    // Close handles to the write pipes, so they are not leaked.
    CloseHandle(stdout_write_pipe);
    CloseHandle(stderr_write_pipe);

#else // Linux/Unix
    if (pipe(stdout_pipe_) == -1 || pipe(stderr_pipe_) == -1) {
        std::cerr << "Failed to create pipes. Error: " << strerror(errno) << std::endl;
        return false;
    }

    child_pid_ = fork();
    if (child_pid_ == -1) {
        std::cerr << "Failed to fork process. Error: " << strerror(errno) << std::endl;
        close(stdout_pipe_[0]); close(stdout_pipe_[1]);
        close(stderr_pipe_[0]); close(stderr_pipe_[1]);
        return false;
    }

    if (child_pid_ == 0) { // Child process
        // Close read ends in child
        close(stdout_pipe_[0]);
        close(stderr_pipe_[0]);

        // Redirect stdout and stderr to pipe write ends
        dup2(stdout_pipe_[1], STDOUT_FILENO);
        dup2(stderr_pipe_[1], STDERR_FILENO);

        // Close write ends after dup2
        close(stdout_pipe_[1]);
        close(stderr_pipe_[1]);

        // Prepare arguments for exec
        std::vector<char*> argv_c_str;
        argv_c_str.push_back(const_cast<char*>(executable_path.c_str()));
        for (const auto& arg : args) {
            argv_c_str.push_back(const_cast<char*>(arg.c_str()));
        }
        argv_c_str.push_back(NULL); // Null-terminate the array

        // Execute the program
        execvp(executable_path.c_str(), argv_c_str.data());

        // If execvp returns, an error occurred
        std::cerr << "Failed to execute process: " << strerror(errno) << ". Executable: " << executable_path << std::endl;
        _exit(1); // Exit child process
    } else { // Parent process
        std::cout << "Successfully forked process with PID: " << child_pid_ << ". Executable: " << executable_path << std::endl;
        // Close write ends in parent
        close(stdout_pipe_[1]);
        close(stderr_pipe_[1]);
    }
#endif

    should_stop_.store(false);
    input_active_.store(true);

    input_thread_ = std::make_unique<std::thread>(&ExtProcessMIDIInput::InputThreadFunction, this);
    stderr_thread_ = std::make_unique<std::thread>(&ExtProcessMIDIInput::StderrThreadFunction, this);

    std::cout << "External Process MIDI input started." << std::endl;
    return true;
}

void ExtProcessMIDIInput::StopInput() {
    if (input_active_.load()) {
        should_stop_.store(true);

        if (input_thread_ && input_thread_->joinable()) {
            input_thread_->join();
        }
        input_thread_.reset();

        if (stderr_thread_ && stderr_thread_->joinable()) {
            stderr_thread_->join();
        }
        stderr_thread_.reset();

#ifdef _WIN32
        if (pi_proc_info_.hProcess != NULL) {
            // Terminate the process if it's still running
            if (WaitForSingleObject(pi_proc_info_.hProcess, 0) == WAIT_TIMEOUT) {
                TerminateProcess(pi_proc_info_.hProcess, 0);
            }
            CloseHandle(pi_proc_info_.hProcess);
            ZeroMemory(&pi_proc_info_, sizeof(PROCESS_INFORMATION));
        }
        if (pi_proc_info_.hThread != NULL) {
            CloseHandle(pi_proc_info_.hThread);
        }
        if (child_stdout_read_ != NULL) {
            CloseHandle(child_stdout_read_);
            child_stdout_read_ = NULL;
        }
        if (child_stderr_read_ != NULL) {
            CloseHandle(child_stderr_read_);
            child_stderr_read_ = NULL;
        }
#else // Linux/Unix
        if (child_pid_ != -1) {
            // Send SIGTERM to the child process
            kill(child_pid_, SIGTERM);
            int status;
            // Wait for the child to terminate
            waitpid(child_pid_, &status, 0);
            child_pid_ = -1;
        }
        if (stdout_pipe_[0] != -1) {
            close(stdout_pipe_[0]);
            stdout_pipe_[0] = -1;
        }
        if (stderr_pipe_[0] != -1) {
            close(stderr_pipe_[0]);
            stderr_pipe_[0] = -1;
        }
#endif

        input_active_.store(false);
        std::cout << "External Process MIDI input stopped." << std::endl;
    }
}

bool ExtProcessMIDIInput::IsInputActive() const {
    return input_active_.load();
}

void ExtProcessMIDIInput::SetMIDICallback(ExtProcessMIDIInputCallback callback) {
    midi_callback_ = callback;
}

std::string ExtProcessMIDIInput::GetStderrLog() const {
    std::lock_guard<std::mutex> lock(stderr_mutex_);
    return stderr_log_;
}

void ExtProcessMIDIInput::ClearStderrLog() {
    std::lock_guard<std::mutex> lock(stderr_mutex_);
    stderr_log_.clear();
}

void ExtProcessMIDIInput::InputThreadFunction() {
#ifdef _WIN32
    DWORD bytes_read;
    CHAR buffer[1024];
    std::string line_buffer;

    while (!should_stop_.load()) {
        if (child_stdout_read_ == NULL) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        // PeekNamedPipe to check for available data without blocking
        DWORD available_bytes = 0;
        if (!PeekNamedPipe(child_stdout_read_, NULL, 0, NULL, &available_bytes, NULL)) {
            // Error or pipe closed
            if (GetLastError() == ERROR_BROKEN_PIPE) {
                std::cerr << "Stdout pipe broken, external process likely terminated." << std::endl;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        if (available_bytes == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        if (ReadFile(child_stdout_read_, buffer, sizeof(buffer) - 1, &bytes_read, NULL)) {
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0'; // Null-terminate the buffer
                line_buffer.append(buffer, bytes_read);

                size_t pos;
                while ((pos = line_buffer.find('\n')) != std::string::npos) {
                    std::string line = line_buffer.substr(0, pos);
                    line_buffer.erase(0, pos + 1);
                    if (midi_callback_) {
                        midi_callback_(ParseMIDILine(line));
                    }
                }
            } else {
                // ReadFile returns true but bytes_read is 0 when pipe is closed
                std::cerr << "Stdout pipe closed, external process likely terminated." << std::endl;
                break;
            }
        } else {
            // ReadFile failed
            if (GetLastError() == ERROR_BROKEN_PIPE) {
                std::cerr << "Stdout pipe broken, external process likely terminated." << std::endl;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
#else // Linux/Unix
    char buffer[1024];
    std::string line_buffer;
    struct pollfd pfd;
    pfd.fd = stdout_pipe_[0];
    pfd.events = POLLIN;

    while (!should_stop_.load()) {
        if (stdout_pipe_[0] == -1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        int poll_ret = poll(&pfd, 1, 10); // 10ms timeout
        if (poll_ret == -1) {
            if (errno == EINTR) continue;
            std::cerr << "Poll error on stdout: " << strerror(errno) << std::endl;
            break;
        } else if (poll_ret == 0) {
            // Timeout, no data
            continue;
        }

        if (pfd.revents & POLLIN) {
            ssize_t bytes_read = read(stdout_pipe_[0], buffer, sizeof(buffer) - 1);
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                line_buffer.append(buffer, bytes_read);

                size_t pos;
                while ((pos = line_buffer.find('\n')) != std::string::npos) {
                    std::string line = line_buffer.substr(0, pos);
                    line_buffer.erase(0, pos + 1);
                    if (midi_callback_) {
                        midi_callback_(ParseMIDILine(line));
                    }
                }
            } else if (bytes_read == 0) {
                // EOF, pipe closed
                std::cerr << "Stdout pipe closed, external process likely terminated." << std::endl;
                break;
            } else {
                // Error
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    // No data available right now, continue
                } else {
                    std::cerr << "Error reading from stdout pipe: " << strerror(errno) << std::endl;
                    break;
                }
            }
        }
    }
#endif
    // If the loop exits, the process has likely terminated or an error occurred.
    // Ensure input_active_ is set to false.
    input_active_.store(false);
}

void ExtProcessMIDIInput::StderrThreadFunction() {
#ifdef _WIN32
    DWORD bytes_read;
    CHAR buffer[1024];

    while (!should_stop_.load()) {
        if (child_stderr_read_ == NULL) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        DWORD available_bytes = 0;
        if (!PeekNamedPipe(child_stderr_read_, NULL, 0, NULL, &available_bytes, NULL)) {
            if (GetLastError() == ERROR_BROKEN_PIPE) {
                std::cerr << "Stderr pipe broken, external process likely terminated." << std::endl;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        if (available_bytes == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        if (ReadFile(child_stderr_read_, buffer, sizeof(buffer) - 1, &bytes_read, NULL)) {
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                std::lock_guard<std::mutex> lock(stderr_mutex_);
                stderr_log_.append(buffer, bytes_read);
            } else {
                std::cerr << "Stderr pipe closed, external process likely terminated." << std::endl;
                break;
            }
        }
        else {
            if (GetLastError() == ERROR_BROKEN_PIPE) {
                std::cerr << "Stderr pipe broken, external process likely terminated." << std::endl;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
#else // Linux/Unix
    char buffer[1024];
    struct pollfd pfd;
    pfd.fd = stderr_pipe_[0];
    pfd.events = POLLIN;

    while (!should_stop_.load()) {
        if (stderr_pipe_[0] == -1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        int poll_ret = poll(&pfd, 1, 10); // 10ms timeout
        if (poll_ret == -1) {
            if (errno == EINTR) continue;
            std::cerr << "Poll error on stderr: " << strerror(errno) << std::endl;
            break;
        } else if (poll_ret == 0) {
            // Timeout, no data
            continue;
        }

        if (pfd.revents & POLLIN) {
            ssize_t bytes_read = read(stderr_pipe_[0], buffer, sizeof(buffer) - 1);
            if (bytes_read > 0) {
                buffer[bytes_read] = '\0';
                std::lock_guard<std::mutex> lock(stderr_mutex_);
                stderr_log_.append(buffer, bytes_read);
            } else if (bytes_read == 0) {
                // EOF, pipe closed
                std::cerr << "Stderr pipe closed, external process likely terminated." << std::endl;
                break;
            } else {
                // Error
                if (errno == EAGAIN || errno == EWOULDBLOCK) {
                    // No data available right now, continue
                } else {
                    std::cerr << "Error reading from stderr pipe: " << strerror(errno) << std::endl;
                    break;
                }
            }
        }
    }
#endif
}

MIDIMessage ExtProcessMIDIInput::ParseMIDILine(const std::string& line) {
    MIDIMessage message = {};
    std::istringstream iss(line);
    std::string segment;

    // Expecting format "status,data1,data2" in hexadecimal
    if (std::getline(iss, segment, ',')) {
        try {
            message.status = static_cast<unsigned char>(std::stoul(segment, nullptr, 16));
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse status (hex): " << segment << " in line: " << line << " Error: " << e.what() << std::endl;
            return message;
        }
    } else {
        std::cerr << "Failed to parse status from line: " << line << std::endl;
        return message;
    }

    if (std::getline(iss, segment, ',')) {
        try {
            message.data1 = static_cast<unsigned char>(std::stoul(segment, nullptr, 16));
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse data1 (hex): " << segment << " in line: " << line << " Error: " << e.what() << std::endl;
            return message;
        }
    } else {
        std::cerr << "Failed to parse data1 from line: " << line << std::endl;
        return message;
    }

    if (std::getline(iss, segment)) { // Read till end of line for data2
        try {
            message.data2 = static_cast<unsigned char>(std::stoul(segment, nullptr, 16));
        } catch (const std::exception& e) {
            std::cerr << "Failed to parse data2 (hex): " << segment << " in line: " << line << " Error: " << e.what() << std::endl;
            return message;
        }
    } else {
        std::cerr << "Failed to parse data2 from line: " << line << std::endl;
        return message;
    }

    message.timestamp = std::chrono::duration<double>(std::chrono::high_resolution_clock::now().time_since_epoch()).count();

    return message;
}
